﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
WindowsApp1
</name>
</assembly>
<members>
<member name="T:WindowsApp1.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:WindowsApp1.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:WindowsApp1.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
</members>
</doc>
