﻿Public Class Form1
    Private Const WM_NCHITTEST As Integer = &H84
    Private Const HTCLIENT As Integer = &H1
    Private Const HTCAPTION As Integer = &H2

    ' 菜单项名称数组
    Private fileButtonNames() As String = {
        "文件压缩", "批量重命名", "文件合并", "文件分割", 
        "格式转换", "文件搜索", "文件比较", "文件加密", 
        "文件解密", "文件备份", "文件检查", "文件预览"
    }

    Private excelButtonNames() As String = {
        "Excel处理", "数据提取", "数据分析", "图表生成", 
        "格式转换", "数据筛选", "数据排序", "数据合并", 
        "数据清洗", "数据导出", "数据校验", "数据可视化"
    }

    ' 在类级别添加主题设置面板
    Private pnlSettings As Panel
    ' 添加标题标签声明
    Private lblTitle As Label
    
    ' 添加文件和Excel功能面板
    Private pnlFileFunctions As Panel
    Private pnlExcelFunctions As Panel
    
    ' 添加主按钮
    Private btnFileManage As Button
    Private btnExcelOps As Button
    Private btnSettings As Button
    Private btnAbout As Button
    
    ' 自定义滚动条结构
    Private pnlMenuContent As Panel ' 内部Panel，所有菜单按钮都加到这里
    
    ' 添加动画计时器
    Private WithEvents tmrFileAnimation As New Timer()
    Private WithEvents tmrExcelAnimation As New Timer()
    Private fileTargetHeight As Integer = 0
    Private excelTargetHeight As Integer = 0
    Private Const AnimationStep As Integer = 20 ' 动画步长

    ' 当前选中的子按钮
    Private currentSelectedButton As Button = Nothing



    ' 自定义滚动条变量
    Private pnlMenuContent As Panel
    Private scrollOffset As Integer = 0
    Private contentHeight As Integer = 0
    Private visibleHeight As Integer = 0
    Private thumbHeight As Integer = 0
    Private thumbTop As Integer = 0
    Private isDraggingThumb As Boolean = False
    Private dragStartY As Integer = 0
    Private dragStartOffset As Integer = 0

    Private Sub Form1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' 设置主窗体为标准边框
        Me.FormBorderStyle = FormBorderStyle.Sizable
        Me.WindowState = FormWindowState.Normal

        ' 禁用系统滚动条，使用自定义滚动条
        pnlMenu.AutoScroll = False
        pnlMenu.AutoScrollMinSize = New Size(0, 0)
        pnlMenu.BorderStyle = BorderStyle.None
        pnlMenu.Padding = New Padding(0, 10, 0, 10)

        InitializeHeaderPanel()
        InitializeMenuPanel()
        InitializeSettingsPanel()
        SetupAnimation() ' 设置动画
        ApplyDarkTheme()  ' 设置初始主题为深色

        ' 注册自定义滚动条事件
        AddHandler pnlMenu.Paint, AddressOf pnlMenu_Paint
        AddHandler pnlMenu.MouseDown, AddressOf pnlMenu_MouseDown
        AddHandler pnlMenu.MouseMove, AddressOf pnlMenu_MouseMove
        AddHandler pnlMenu.MouseUp, AddressOf pnlMenu_MouseUp
        AddHandler pnlMenu.MouseWheel, AddressOf pnlMenu_MouseWheel
        AddHandler pnlMenu.Resize, AddressOf pnlMenu_Resize

        ' 监听内容变化（如菜单展开/收起）
        AddHandler pnlFileFunctions.SizeChanged, Sub(s, ev) UpdateScrollBars()
        AddHandler pnlExcelFunctions.SizeChanged, Sub(s, ev) UpdateScrollBars()
    End Sub

    ' 处理窗体大小变化
    Private Sub Form1_Resize(sender As Object, e As EventArgs) Handles Me.Resize
        ' 更新菜单面板布局
        pnlMenu.PerformLayout()
    End Sub

    ' 设置动画计时器
    Private Sub SetupAnimation()
        ' 文件菜单动画
        tmrFileAnimation.Interval = 15 ' 15毫秒刷新一次
        tmrFileAnimation.Enabled = False

        ' Excel菜单动画
        tmrExcelAnimation.Interval = 15
        tmrExcelAnimation.Enabled = False

        ' 初始隐藏子菜单
        pnlFileFunctions.Height = 0
        pnlFileFunctions.Visible = False
        pnlExcelFunctions.Height = 0
        pnlExcelFunctions.Visible = False
    End Sub

    ' 初始化菜单面板
    Private Sub InitializeMenuPanel()
        ' 创建内部面板用于存放所有菜单按钮
        pnlMenuContent = New Panel With {
            .Left = 0,
            .Top = 0,
            .Width = pnlMenu.Width - 8, ' 留出滚动条空间
            .AutoSize = False,
            .BackColor = Color.Transparent
        }
        pnlMenu.Controls.Add(pnlMenuContent)
        
        ' 主按钮
        btnFileManage = New Button With {
            .Text = "  📁 文件管理",
            .FlatStyle = FlatStyle.Flat,
            .TextAlign = ContentAlignment.MiddleLeft,
            .Font = New Font("微软雅黑", 10, FontStyle.Bold),
            .Dock = DockStyle.Top,
            .Height = 40
        }
        btnFileManage.FlatAppearance.BorderSize = 0

        btnExcelOps = New Button With {
            .Text = "  📊 Excel操作",
            .FlatStyle = FlatStyle.Flat,
            .TextAlign = ContentAlignment.MiddleLeft,
            .Font = New Font("微软雅黑", 10, FontStyle.Bold),
            .Dock = DockStyle.Top,
            .Height = 40
        }
        btnExcelOps.FlatAppearance.BorderSize = 0

        ' 文件功能面板
        pnlFileFunctions = New Panel With {
            .Dock = DockStyle.Top,
            .Height = 0,
            .Visible = False
        }

        ' Excel功能面板
        pnlExcelFunctions = New Panel With {
            .Dock = DockStyle.Top,
            .Height = 0,
            .Visible = False
        }

        ' 设置按钮
        btnSettings = New Button With {
            .Text = "  ⚙️ 设置",
            .FlatStyle = FlatStyle.Flat,
            .TextAlign = ContentAlignment.MiddleLeft,
            .Font = New Font("微软雅黑", 10, FontStyle.Bold),
            .Dock = DockStyle.Top,
            .Height = 40
        }
        btnSettings.FlatAppearance.BorderSize = 0

        ' 关于按钮
        btnAbout = New Button With {
            .Text = "  ℹ️ 关于",
            .FlatStyle = FlatStyle.Flat,
            .TextAlign = ContentAlignment.MiddleLeft,
            .Font = New Font("微软雅黑", 10, FontStyle.Bold),
            .Dock = DockStyle.Top,
            .Height = 40
        }
        btnAbout.FlatAppearance.BorderSize = 0

        ' 文件管理子按钮
        pnlFileFunctions.Controls.Clear()
        For Each buttonText As String In fileButtonNames
            Dim btn As New Button With {
                .Text = "          " & buttonText,
                .FlatStyle = FlatStyle.Flat,
                .TextAlign = ContentAlignment.MiddleLeft,
                .Font = New Font("微软雅黑", 9),
                .Dock = DockStyle.Top,
                .Height = 35
            }
           btn.FlatAppearance.BorderSize = 0
            AddHandler btn.Click, AddressOf SubButton_Click
            pnlFileFunctions.Controls.Add(btn)
        Next

        ' Excel操作子按钮
        pnlExcelFunctions.Controls.Clear()
        For Each buttonText As String In excelButtonNames
            Dim btn As New Button With {
                .Text = "          " & buttonText,
                .FlatStyle = FlatStyle.Flat,
                .TextAlign = ContentAlignment.MiddleLeft,
                .Font = New Font("微软雅黑", 9),
                .Dock = DockStyle.Top,
                .Height = 35
            }
            btn.FlatAppearance.BorderSize = 0
            AddHandler btn.Click, AddressOf SubButton_Click
            pnlExcelFunctions.Controls.Add(btn)
        Next

        ' 添加事件处理
        AddHandler btnFileManage.Click, AddressOf btnFileManage_Click
        AddHandler btnExcelOps.Click, AddressOf btnExcelOps_Click
        AddHandler btnSettings.Click, AddressOf btnSettings_Click
        AddHandler btnAbout.Click, AddressOf btnAbout_Click

        ' 添加控件到内部面板
        pnlMenuContent.Controls.Add(btnAbout)
        pnlMenuContent.Controls.Add(btnSettings)
        pnlMenuContent.Controls.Add(pnlExcelFunctions)
        pnlMenuContent.Controls.Add(btnExcelOps)
        pnlMenuContent.Controls.Add(pnlFileFunctions)
        pnlMenuContent.Controls.Add(btnFileManage)
        
        ' 初始化滚动条
        UpdateScrollBars()
    End Sub

    ' 子按钮点击事件
    Private Sub SubButton_Click(sender As Object, e As EventArgs)
        Dim btn As Button = DirectCast(sender, Button)

        ' 如果有之前选中的按钮，恢复其原始颜色
        If currentSelectedButton IsNot Nothing Then
            If IsDarkTheme() Then
                currentSelectedButton.BackColor = Color.FromArgb(38, 38, 48)
                currentSelectedButton.ForeColor = Color.White
            Else
                currentSelectedButton.BackColor = Color.FromArgb(240, 240, 245) ' 浅色背景
                currentSelectedButton.ForeColor = Color.Black
            End If
        End If

        ' 设置当前按钮为选中状态 - 使用与主题一致的颜色
        currentSelectedButton = btn
        If IsDarkTheme() Then
            ' 深色主题下使用深色的选中颜色
            btn.BackColor = Color.FromArgb(50, 50, 60)
        Else
            ' 浅色主题下使用浅色的选中颜色 - 调整为图片中类似的颜色
            btn.BackColor = Color.FromArgb(210, 215, 235) ' 浅蓝色高亮
        End If

        MessageBox.Show("点击了: " & btn.Text.Trim())
    End Sub

    ' 文件管理按钮点击事件
    Private Sub btnFileManage_Click(sender As Object, e As EventArgs)
        ' 计算可用空间 - 不再考虑其他菜单的状态
        Dim totalButtonsHeight As Integer = fileButtonNames.Length * 35

        If Not pnlFileFunctions.Visible Then
            ' 展开菜单
            pnlFileFunctions.Height = 0
            pnlFileFunctions.Visible = True
            fileTargetHeight = totalButtonsHeight
            tmrFileAnimation.Enabled = True
            btnFileManage.Text = "  📁 文件管理 ﹀"
        Else
            ' 收起菜单
            fileTargetHeight = 0
            tmrFileAnimation.Enabled = True
            btnFileManage.Text = "  📁 文件管理 ︿"
        End If

        ' 立即更新滚动条
        UpdateScrollBars()
    End Sub

    ' Excel操作按钮点击事件
    Private Sub btnExcelOps_Click(sender As Object, e As EventArgs)
        ' 计算可用空间 - 不再考虑其他菜单的状态
        Dim totalButtonsHeight As Integer = excelButtonNames.Length * 35

        If Not pnlExcelFunctions.Visible Then
            ' 展开菜单
            pnlExcelFunctions.Height = 0
            pnlExcelFunctions.Visible = True
            excelTargetHeight = totalButtonsHeight
            tmrExcelAnimation.Enabled = True
            btnExcelOps.Text = "  📊 Excel操作 ﹀"
        Else
            ' 收起菜单
            excelTargetHeight = 0
            tmrExcelAnimation.Enabled = True
            btnExcelOps.Text = "  📊 Excel操作 ︿"
        End If

        ' 立即更新滚动条
        UpdateScrollBars()
    End Sub

    ' 设置按钮点击事件
    Private Sub btnSettings_Click(sender As Object, e As EventArgs)
        pnlSettings.Visible = Not pnlSettings.Visible
    End Sub

    ' 关于按钮点击事件
    Private Sub btnAbout_Click(sender As Object, e As EventArgs)
        MessageBox.Show("管理系统 v1.0" & vbCrLf & "Copyright © 2024", "关于", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    ' 更新自定义滚动条
    Private Sub UpdateScrollBars()
        If pnlMenuContent Is Nothing Then Return

        ' 计算内容总高度
        contentHeight = 0
        For Each ctrl As Control In pnlMenuContent.Controls
            contentHeight += ctrl.Height
        Next

        visibleHeight = pnlMenu.ClientSize.Height

        If contentHeight > visibleHeight Then
            ' 计算滚动条thumb高度和位置
            thumbHeight = Math.Max(20, CInt((visibleHeight * visibleHeight) / contentHeight))
            thumbTop = CInt((scrollOffset * (visibleHeight - thumbHeight)) / (contentHeight - visibleHeight))
        Else
            thumbHeight = 0
            thumbTop = 0
            scrollOffset = 0
        End If

        ' 更新内部面板位置
        pnlMenuContent.Top = -scrollOffset
        pnlMenuContent.Height = Math.Max(contentHeight, visibleHeight)

        ' 重绘滚动条
        pnlMenu.Invalidate()
    End Sub

    ' 文件菜单动画计时器
    Private Sub tmrFileAnimation_Tick(sender As Object, e As EventArgs) Handles tmrFileAnimation.Tick
        If fileTargetHeight > pnlFileFunctions.Height Then
            ' 展开菜单
            pnlFileFunctions.Height += AnimationStep
            If pnlFileFunctions.Height >= fileTargetHeight Then
                pnlFileFunctions.Height = fileTargetHeight
                tmrFileAnimation.Enabled = False
            End If
        ElseIf fileTargetHeight < pnlFileFunctions.Height Then
            ' 收起菜单
            pnlFileFunctions.Height -= AnimationStep
            If pnlFileFunctions.Height <= 0 Then
                pnlFileFunctions.Height = 0
                pnlFileFunctions.Visible = False
                tmrFileAnimation.Enabled = False
            End If
        Else
            tmrFileAnimation.Enabled = False
        End If

        ' 更新滚动条可见性
        UpdateScrollVisibility()
        ' hack: 再次隐藏系统滚动条
        HidePanelScrollBar()
    End Sub

    ' Excel菜单动画计时器
    Private Sub tmrExcelAnimation_Tick(sender As Object, e As EventArgs) Handles tmrExcelAnimation.Tick
        If excelTargetHeight > pnlExcelFunctions.Height Then
            ' 展开菜单
            pnlExcelFunctions.Height += AnimationStep
            If pnlExcelFunctions.Height >= excelTargetHeight Then
                pnlExcelFunctions.Height = excelTargetHeight
                tmrExcelAnimation.Enabled = False
            End If
        ElseIf excelTargetHeight < pnlExcelFunctions.Height Then
            ' 收起菜单
            pnlExcelFunctions.Height -= AnimationStep
            If pnlExcelFunctions.Height <= 0 Then
            pnlExcelFunctions.Height = 0
            pnlExcelFunctions.Visible = False
                tmrExcelAnimation.Enabled = False
            End If
        Else
            tmrExcelAnimation.Enabled = False
        End If

        ' 更新滚动条可见性
        UpdateScrollVisibility()
        ' hack: 再次隐藏系统滚动条
        HidePanelScrollBar()
    End Sub

    ' 添加初始化标题面板的方法
    Private Sub InitializeHeaderPanel()
        ' 创建标题标签
        lblTitle = New Label With {
            .Text = "管理系统",
            .Font = New Font("微软雅黑", 12, FontStyle.Bold),
            .ForeColor = Color.White,
            .AutoSize = True,
            .Location = New Point(10, 10)
        }

        ' 将标题标签添加到标题面板
        pnlHeader.Controls.Add(lblTitle)
    End Sub

    ' 应用深色主题
    Private Sub ApplyDarkTheme()
        ' 主窗体
        BackColor = Color.FromArgb(32, 32, 40)
        
        ' 主要面板
        pnlMenu.BackColor = Color.FromArgb(32, 32, 40)
        pnlSettings.BackColor = Color.FromArgb(38, 38, 48)
        pnlFileFunctions.BackColor = Color.FromArgb(38, 38, 48)
        pnlExcelFunctions.BackColor = Color.FromArgb(38, 38, 48)
        
        ' 标题栏颜色
        pnlHeader.BackColor = Color.FromArgb(38, 38, 48)
        lblTitle.ForeColor = Color.White

        ' 确保窗体中所有面板都更新颜色
        For Each ctrl As Control In Me.Controls
            If TypeOf ctrl Is Panel Then
                ' 跳过已经设置过的面板
                If ctrl IsNot pnlMenu AndAlso ctrl IsNot pnlSettings AndAlso
                   ctrl IsNot pnlFileFunctions AndAlso ctrl IsNot pnlExcelFunctions AndAlso
                   ctrl IsNot pnlHeader Then
                    ' 对于未明确设置的面板，设置为深色背景
                    ctrl.BackColor = Color.FromArgb(32, 32, 40)
                End If
            End If
        Next
        
        ' 设置按钮颜色
        If btnFileManage IsNot Nothing Then
            btnFileManage.ForeColor = Color.White
            btnFileManage.BackColor = Color.FromArgb(32, 32, 40)
            ' 使用深色主题的悬停效果
            AddButtonHoverEffect(btnFileManage, Color.FromArgb(58, 58, 68), Color.White)
        End If
        
        If btnExcelOps IsNot Nothing Then
            btnExcelOps.ForeColor = Color.White
            btnExcelOps.BackColor = Color.FromArgb(32, 32, 40)
            ' 使用深色主题的悬停效果
            AddButtonHoverEffect(btnExcelOps, Color.FromArgb(58, 58, 68), Color.White)
        End If
        
        If btnSettings IsNot Nothing Then
            btnSettings.ForeColor = Color.White
            btnSettings.BackColor = Color.FromArgb(32, 32, 40)
            ' 使用深色主题的悬停效果
            AddButtonHoverEffect(btnSettings, Color.FromArgb(58, 58, 68), Color.White)
        End If
        
        If btnAbout IsNot Nothing Then
            btnAbout.ForeColor = Color.White
            btnAbout.BackColor = Color.FromArgb(32, 32, 40)
            ' 使用深色主题的悬停效果
            AddButtonHoverEffect(btnAbout, Color.FromArgb(58, 58, 68), Color.White)
        End If
        
        ' 设置子按钮颜色
        For Each ctrl As Control In pnlFileFunctions.Controls
            If TypeOf ctrl Is Button Then
                Dim btn As Button = DirectCast(ctrl, Button)
                btn.ForeColor = Color.White
                btn.BackColor = Color.FromArgb(38, 38, 48)
                ' 使用深色主题的悬停效果
                AddButtonHoverEffect(btn, Color.FromArgb(58, 58, 68), Color.White)
            End If
        Next
        
        For Each ctrl As Control In pnlExcelFunctions.Controls
            If TypeOf ctrl Is Button Then
                Dim btn As Button = DirectCast(ctrl, Button)
                btn.ForeColor = Color.White
                btn.BackColor = Color.FromArgb(38, 38, 48)
                ' 使用深色主题的悬停效果
                AddButtonHoverEffect(btn, Color.FromArgb(58, 58, 68), Color.White)
            End If
        Next
        
        ' 更新设置面板中的控件颜色
        For Each ctrl As Control In pnlSettings.Controls
            If TypeOf ctrl Is Label Then
                DirectCast(ctrl, Label).ForeColor = Color.White
            ElseIf TypeOf ctrl Is RadioButton Then
                DirectCast(ctrl, RadioButton).ForeColor = Color.White
            End If
        Next
        
        ' 设置自定义滚动条颜色 - 使用更适合深色主题的颜色
        ' 移除对CustomScrollPanel的引用，不再设置自定义滚动条
    End Sub

    ' 应用浅色主题
    Private Sub ApplyLightTheme()
        ' 浅色主题的统一颜色
        Dim lightBackColor As Color = Color.FromArgb(240, 240, 245)       ' 背景色（更浅）
        Dim lightPanelColor As Color = Color.FromArgb(225, 225, 235)      ' 面板色（稍深）
        Dim lightHighlightColor As Color = Color.FromArgb(75, 110, 175)   ' 高亮色（蓝色）
        Dim lightHoverColor As Color = Color.FromArgb(210, 215, 225)      ' 鼠标悬停色（浅灰蓝）
        
        ' 主窗体
        BackColor = lightBackColor

        ' 主要面板
        pnlMenu.BackColor = lightPanelColor
        pnlSettings.BackColor = lightPanelColor
        pnlFileFunctions.BackColor = lightBackColor
        pnlExcelFunctions.BackColor = lightBackColor
        pnlHeader.BackColor = lightPanelColor

        ' 标题文本颜色
        lblTitle.ForeColor = Color.Black

        ' 确保窗体中所有面板都更新颜色
        For Each ctrl As Control In Me.Controls
            If TypeOf ctrl Is Panel Then
                ctrl.BackColor = lightBackColor
            End If
        Next
        
        ' 设置按钮颜色
        If btnFileManage IsNot Nothing Then
            btnFileManage.ForeColor = Color.Black
            btnFileManage.BackColor = lightPanelColor
            ' 使用浅色主题的悬停效果
            AddButtonHoverEffect(btnFileManage, lightHoverColor, Color.Black)
        End If
        
        If btnExcelOps IsNot Nothing Then
            btnExcelOps.ForeColor = Color.Black
            btnExcelOps.BackColor = lightPanelColor
            ' 使用浅色主题的悬停效果
            AddButtonHoverEffect(btnExcelOps, lightHoverColor, Color.Black)
        End If
        
        If btnSettings IsNot Nothing Then
            btnSettings.ForeColor = Color.Black
            btnSettings.BackColor = lightPanelColor
            ' 使用浅色主题的悬停效果
            AddButtonHoverEffect(btnSettings, lightHoverColor, Color.Black)
        End If
        
        If btnAbout IsNot Nothing Then
            btnAbout.ForeColor = Color.Black
            btnAbout.BackColor = lightPanelColor
            ' 使用浅色主题的悬停效果
            AddButtonHoverEffect(btnAbout, lightHoverColor, Color.Black)
        End If
        
        ' 设置子按钮颜色
        For Each ctrl As Control In pnlFileFunctions.Controls
            If TypeOf ctrl Is Button Then
                Dim btn As Button = DirectCast(ctrl, Button)
                btn.ForeColor = Color.Black
                btn.BackColor = lightBackColor
                ' 使用浅色主题的悬停效果
                AddButtonHoverEffect(btn, lightHoverColor, Color.Black)
            End If
        Next
        
        For Each ctrl As Control In pnlExcelFunctions.Controls
            If TypeOf ctrl Is Button Then
                Dim btn As Button = DirectCast(ctrl, Button)
                btn.ForeColor = Color.Black
                btn.BackColor = lightBackColor
                ' 使用浅色主题的悬停效果
                AddButtonHoverEffect(btn, lightHoverColor, Color.Black)
            End If
        Next
        
        ' 更新设置面板中的控件颜色
        For Each ctrl As Control In pnlSettings.Controls
            If TypeOf ctrl Is Label Then
                DirectCast(ctrl, Label).ForeColor = Color.Black
            ElseIf TypeOf ctrl Is RadioButton Then
                DirectCast(ctrl, RadioButton).ForeColor = Color.Black
            End If
        Next
        
        ' 设置自定义滚动条颜色 - 使用更适合浅色主题的颜色
        ' 移除对CustomScrollPanel的引用，不再设置自定义滚动条
    End Sub

    ' 为按钮添加鼠标悬停效果
    Private Sub AddButtonHoverEffect(btn As Button, hoverColor As Color, textColor As Color)
        ' 检查是否已经添加了悬停效果
        If btn.Tag IsNot Nothing AndAlso btn.Tag.ToString() = "HoverEffectAdded" Then
            Return
        End If

        ' 添加鼠标进入事件处理程序
        AddHandler btn.MouseEnter, Sub(sender, e)
                                       Dim b As Button = DirectCast(sender, Button)
                                       ' 如果是当前选中的按钮，不改变颜色
                                       If b Is currentSelectedButton Then
                                           Return
                                       End If
                                       b.BackColor = hoverColor
                                       b.ForeColor = textColor
                                       b.Cursor = Cursors.Hand
                                   End Sub

        ' 添加鼠标离开事件处理程序
        AddHandler btn.MouseLeave, Sub(sender, e)
                                       Dim b As Button = DirectCast(sender, Button)
                                       ' 如果是当前选中的按钮，不改变颜色
                                       If b Is currentSelectedButton Then
                                           Return
                                       End If

                                       If pnlFileFunctions.Visible AndAlso (b Is btnFileManage) Then
                                           ' 保持高亮状态
                                           Return
                                       ElseIf pnlExcelFunctions.Visible AndAlso (b Is btnExcelOps) Then
                                           ' 保持高亮状态
                                           Return
                                       End If

                                       ' 恢复原始颜色
                                       If b.Parent Is pnlFileFunctions OrElse b.Parent Is pnlExcelFunctions Then
                                           ' 子按钮
                                           If IsDarkTheme() Then
                                               b.BackColor = Color.FromArgb(38, 38, 48)
                                               b.ForeColor = Color.White
                                           Else
                                               b.BackColor = Color.FromArgb(240, 240, 245) ' 浅色背景
                                               b.ForeColor = Color.Black
                                           End If
                                       Else
                                           ' 主按钮
                                           If IsDarkTheme() Then
                                               b.BackColor = Color.FromArgb(32, 32, 40)
                                           Else
                                               b.BackColor = Color.FromArgb(240, 240, 245) ' 浅色背景
                                           End If
                                       End If
                                       
                                       b.Cursor = Cursors.Default
                                   End Sub
        
        ' 标记按钮已添加悬停效果
        btn.Tag = "HoverEffectAdded"
    End Sub
    
    ' 判断当前是否为深色主题
    Private Function IsDarkTheme() As Boolean
        ' 通过背景色判断当前主题
        Return BackColor.R < 100 AndAlso BackColor.G < 100 AndAlso BackColor.B < 100
    End Function

    ' 添加设置面板初始化方法
    Private Sub InitializeSettingsPanel()
        ' 创建设置面板
        pnlSettings = New Panel With {
            .Size = New Size(950, 800),  ' 右侧区域大小
            .Location = New Point(250, 0),  ' 位于左侧菜单旁边
            .Visible = False,
            .BackColor = Color.FromArgb(38, 38, 48)
        }

        ' 创建主题设置组
        Dim lblTheme As New Label With {
            .Text = "主题设置:",
            .Font = New Font("微软雅黑", 12),
            .ForeColor = Color.White,
            .Location = New Point(30, 30),
            .AutoSize = True
        }

        Dim rbDark As New RadioButton With {
            .Text = "深色主题",
            .Font = New Font("微软雅黑", 10),
            .ForeColor = Color.White,
            .Location = New Point(30, 70),
            .Checked = True
        }

        Dim rbLight As New RadioButton With {
            .Text = "浅色主题",
            .Font = New Font("微软雅黑", 10),
            .ForeColor = Color.White,
            .Location = New Point(150, 70)
        }

        ' 添加主题切换事件处理
        AddHandler rbDark.CheckedChanged, Sub(s, e)
                                              If rbDark.Checked Then
                                                  ApplyDarkTheme()
                                              End If
                                          End Sub

        AddHandler rbLight.CheckedChanged, Sub(s, e)
                                               If rbLight.Checked Then
                                                   ApplyLightTheme()
                                               End If
                                           End Sub

        ' 将控件添加到设置面板
        pnlSettings.Controls.AddRange({lblTheme, rbDark, rbLight})

        ' 将设置面板添加到窗体
        Me.Controls.Add(pnlSettings)
    End Sub

    ' 添加窗体拖动功能
    Private isMouseDown As Boolean = False
    Private mouseOffset As Point

    Private Sub pnlHeader_MouseDown(sender As Object, e As MouseEventArgs) Handles pnlHeader.MouseDown
        If e.Button = MouseButtons.Left Then
            isMouseDown = True
            mouseOffset = New Point(-e.X, -e.Y)
        End If
    End Sub

    Private Sub pnlHeader_MouseMove(sender As Object, e As MouseEventArgs) Handles pnlHeader.MouseMove
        If isMouseDown Then
            Dim mousePos As Point = Control.MousePosition
            mousePos.Offset(mouseOffset.X, mouseOffset.Y)
            Me.Location = mousePos
        End If
    End Sub

    Private Sub pnlHeader_MouseUp(sender As Object, e As MouseEventArgs) Handles pnlHeader.MouseUp
        If e.Button = MouseButtons.Left Then
            isMouseDown = False
        End If
    End Sub

    Protected Overrides ReadOnly Property CreateParams As CreateParams
        Get
            Dim cp As CreateParams = MyBase.CreateParams
            cp.Style = cp.Style Or &HC00000
            Return cp
        End Get
    End Property

    Protected Overrides Sub WndProc(ByRef m As Message)
        Select Case m.Msg
            Case WM_NCHITTEST
                MyBase.WndProc(m)
                If m.Result = HTCLIENT Then
                    m.Result = HTCAPTION
                End If
            Case Else
                MyBase.WndProc(m)
        End Select
    End Sub

    ' 3. 添加自定义滚动条的绘制和交互逻辑
    Private Sub pnlMenu_Paint(sender As Object, e As PaintEventArgs)
        Dim totalHeight As Integer = pnlMenu.Controls.Cast(Of Control)().Sum(Function(c) c.Height)
        Dim viewHeight As Integer = pnlMenu.ClientSize.Height
        If totalHeight <= viewHeight Then Return
        Dim thumbHeight As Integer = Math.Max(30, CInt(viewHeight * viewHeight / totalHeight))
        Dim maxThumbY As Integer = viewHeight - thumbHeight
        Dim thumbY As Integer = 0
        If totalHeight - viewHeight > 0 Then
            thumbY = CInt(maxThumbY * scrollOffset / (totalHeight - viewHeight))
        End If
        Dim x As Integer = pnlMenu.Width - 10
        Dim thumbRect As New Rectangle(x, thumbY, 7, thumbHeight)
        Using path As New Drawing2D.GraphicsPath()
            Dim r As Integer = 4
            path.AddArc(thumbRect.X, thumbRect.Y, r * 2, r * 2, 180, 90)
            path.AddArc(thumbRect.Right - r * 2, thumbRect.Y, r * 2, r * 2, 270, 90)
            path.AddArc(thumbRect.Right - r * 2, thumbRect.Bottom - r * 2, r * 2, r * 2, 0, 90)
            path.AddArc(thumbRect.X, thumbRect.Bottom - r * 2, r * 2, r * 2, 90, 90)
            path.CloseFigure()
            Using thumbBrush As New SolidBrush(Color.FromArgb(180, 180, 180))
                e.Graphics.SmoothingMode = Drawing2D.SmoothingMode.AntiAlias
                e.Graphics.FillPath(thumbBrush, path)
            End Using
        End Using
    End Sub

    Private Sub pnlMenu_MouseDown(sender As Object, e As MouseEventArgs)
        Dim totalHeight As Integer = pnlMenu.Controls.Cast(Of Control).Sum(Function(c) c.Height)
        Dim viewHeight As Integer = pnlMenu.ClientSize.Height
        If totalHeight <= viewHeight Then Return
        Dim thumbHeight As Integer = Math.Max(30, CInt(viewHeight * viewHeight / totalHeight))
        Dim maxThumbY As Integer = viewHeight - thumbHeight
        Dim thumbY As Integer = 0
        If totalHeight - viewHeight > 0 Then
            thumbY = CInt(maxThumbY * scrollOffset / (totalHeight - viewHeight))
        End If
        Dim x As Integer = pnlMenu.Width - 10
        Dim thumbRect As New Rectangle(x, thumbY, 7, thumbHeight)
        If thumbRect.Contains(e.Location) Then
            isDraggingThumb = True
            dragStartY = e.Y
            dragStartOffset = scrollOffset
        End If
    End Sub

    Private Sub pnlMenu_MouseMove(sender As Object, e As MouseEventArgs)
        If isDraggingThumb Then
            Dim totalHeight As Integer = pnlMenu.Controls.Cast(Of Control).Sum(Function(c) c.Height)
            Dim viewHeight As Integer = pnlMenu.ClientSize.Height
            Dim thumbHeight As Integer = Math.Max(30, CInt(viewHeight * viewHeight / totalHeight))
            Dim maxOffset As Integer = Math.Max(0, totalHeight - viewHeight)
            Dim maxThumbY As Integer = viewHeight - thumbHeight
            Dim deltaY As Integer = e.Y - dragStartY
            If maxThumbY > 0 Then
                scrollOffset = Math.Min(Math.Max(0, dragStartOffset + CInt(deltaY * maxOffset / maxThumbY)), maxOffset)
                UpdateMenuScroll()
            End If
        End If
    End Sub

    Private Sub pnlMenu_MouseUp(sender As Object, e As MouseEventArgs)
        isDraggingThumb = False
    End Sub

    Private Sub pnlMenu_MouseWheel(sender As Object, e As MouseEventArgs)
        Dim totalHeight As Integer = pnlMenu.Controls.Cast(Of Control).Sum(Function(c) c.Height)
        Dim viewHeight As Integer = pnlMenu.ClientSize.Height
        Dim maxOffset As Integer = Math.Max(0, totalHeight - viewHeight)
        scrollOffset = Math.Min(Math.Max(0, scrollOffset - e.Delta), maxOffset)
        UpdateMenuScroll()
    End Sub

    Private Sub UpdateMenuScroll()
        Dim y As Integer = -scrollOffset
        For Each ctrl As Control In pnlMenu.Controls.Cast(Of Control).Reverse()
            ctrl.Top = y
            y += ctrl.Height
        Next
        pnlMenu.Invalidate()
    End Sub



    ' 自定义滚动条绘制
    Private Sub pnlMenu_Paint(sender As Object, e As PaintEventArgs) Handles pnlMenu.Paint
        If contentHeight <= visibleHeight OrElse thumbHeight = 0 Then Return

        ' 绘制滚动条轨道
        Dim trackRect As New Rectangle(pnlMenu.Width - 8, 0, 8, pnlMenu.Height)
        Using trackBrush As New SolidBrush(Color.FromArgb(30, 0, 0, 0))
            e.Graphics.FillRectangle(trackBrush, trackRect)
        End Using

        ' 绘制滚动条thumb
        If thumbHeight > 0 Then
            Dim thumbRect As New Rectangle(pnlMenu.Width - 7, thumbTop, 6, thumbHeight)
            Using thumbBrush As New SolidBrush(Color.FromArgb(100, 150, 150, 150))
                Dim path As New Drawing2D.GraphicsPath()
                Dim radius As Integer = 3
                
                ' 创建圆角矩形路径
                path.AddArc(thumbRect.X, thumbRect.Y, radius * 2, radius * 2, 180, 90)
                path.AddArc(thumbRect.Right - radius * 2, thumbRect.Y, radius * 2, radius * 2, 270, 90)
                path.AddArc(thumbRect.Right - radius * 2, thumbRect.Bottom - radius * 2, radius * 2, radius * 2, 0, 90)
                path.AddArc(thumbRect.X, thumbRect.Bottom - radius * 2, radius * 2, radius * 2, 90, 90)
                path.CloseFigure()
                
                e.Graphics.SmoothingMode = Drawing2D.SmoothingMode.AntiAlias
                e.Graphics.FillPath(thumbBrush, path)
            End Using
        End If
    End Sub

    ' 鼠标滚轮事件
    Private Sub pnlMenu_MouseWheel(sender As Object, e As MouseEventArgs) Handles pnlMenu.MouseWheel
        If contentHeight <= visibleHeight Then Return

        Dim scrollDelta As Integer = e.Delta * SystemInformation.MouseWheelScrollLines \ 120
        scrollOffset = Math.Max(0, Math.Min(scrollOffset - scrollDelta * 20, contentHeight - visibleHeight))
        UpdateScrollBars()
    End Sub

    ' 鼠标按下事件
    Private Sub pnlMenu_MouseDown(sender As Object, e As MouseEventArgs) Handles pnlMenu.MouseDown
        If e.Button = MouseButtons.Left AndAlso contentHeight > visibleHeight Then
            Dim thumbRect As New Rectangle(pnlMenu.Width - 8, thumbTop, 8, thumbHeight)
            If thumbRect.Contains(e.Location) Then
                isDraggingThumb = True
                dragStartY = e.Y
                dragStartOffset = scrollOffset
                pnlMenu.Capture = True
            End If
        End If
    End Sub

    ' 鼠标移动事件
    Private Sub pnlMenu_MouseMove(sender As Object, e As MouseEventArgs) Handles pnlMenu.MouseMove
        If isDraggingThumb Then
            Dim deltaY As Integer = e.Y - dragStartY
            Dim scrollRange As Integer = visibleHeight - thumbHeight
            Dim contentRange As Integer = contentHeight - visibleHeight
            
            If scrollRange > 0 Then
                scrollOffset = Math.Max(0, Math.Min(dragStartOffset + (deltaY * contentRange \ scrollRange), contentRange))
                UpdateScrollBars()
            End If
        End If
    End Sub

    ' 鼠标释放事件
    Private Sub pnlMenu_MouseUp(sender As Object, e As MouseEventArgs) Handles pnlMenu.MouseUp
        If isDraggingThumb Then
            isDraggingThumb = False
            pnlMenu.Capture = False
        End If
    End Sub

    ' 面板大小改变事件
    Private Sub pnlMenu_Resize(sender As Object, e As EventArgs) Handles pnlMenu.Resize
        If pnlMenuContent IsNot Nothing Then
            pnlMenuContent.Width = pnlMenu.Width - 8
            UpdateScrollBars()
        End If
    End Sub
End Class

Friend NotInheritable Class NativeMethods
    <System.Runtime.InteropServices.DllImport("uxtheme.dll", CharSet:=System.Runtime.InteropServices.CharSet.Unicode)>
    Public Shared Function SetWindowTheme(hwnd As IntPtr, pszSubAppName As String, pszSubIdList As String) As Integer
    End Function
End Class